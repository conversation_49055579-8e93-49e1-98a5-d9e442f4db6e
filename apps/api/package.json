{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "dev": "nest start --watch --port 4000", "start": "nest start", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix --max-warnings 0", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@nestjs/common": "^11.1.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.2", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.2", "@prisma/client": "^6.8.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-linkedin-oauth2": "^2.0.0", "prisma": "^6.8.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.1.2", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-linkedin-oauth2": "^1.5.6", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}