import { NextRequest, NextResponse } from "next/server";
import { locales } from "./i18n/config";

function getBestMatchingLocale(request: NextRequest): string {
  const defaultLocale = "en";

  const acceptLanguage = request.headers.get("Accept-Language");
  if (!acceptLanguage) return defaultLocale;

  const browserLocales = acceptLanguage.split(",").map((item) => {
    const parts = item.split(";");
    const lang = parts[0] ? parts[0].trim() : "";
    const primary = lang.split("-")[0] ?? "";
    return primary;
  });

  for (const browserLocale of browserLocales) {
    if (!browserLocale) continue;

    if (locales.includes(browserLocale as (typeof locales)[number])) {
      return browserLocale;
    }
  }

  return defaultLocale;
}

export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/api") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  if (pathname === "/") {
    const locale = getBestMatchingLocale(request);
    const url = new URL(`/${locale}`, request.url);
    return NextResponse.redirect(url);
  }

  const pathnameIsMissingLocale = locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  if (pathnameIsMissingLocale) {
    const locale = getBestMatchingLocale(request);
    const url = new URL(`/${locale}${pathname}`, request.url);
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Match all paths except for:
    // - API routes (/api/...)
    // - Static files (/_next/...)
    // - Files with extensions (.jpg, .png, etc)
    "/((?!api|_next|_vercel|.*\\..*).*)",
  ],
};
