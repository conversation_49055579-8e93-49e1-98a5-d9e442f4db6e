"use client";

import { useSession } from "next-auth/react";
import { GoogleSignInButton, LinkedInSignInButton, UserProfile } from "./index";

const googleButtonStyle =
  "max-w-xs mx-auto flex justify-center py-1.5 px-3 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500";

const linkedinButtonStyle =
  "max-w-xs mx-auto flex justify-center py-1.5 px-3 border border-transparent rounded-md shadow-sm text-xs font-medium text-white bg-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-blue-500";

export function LoginButtons() {
  const { data: session, status } = useSession();

  if (status === "authenticated" && session) {
    return <UserProfile />;
  }

  return (
    <div className="space-y-3 mt-4 max-w-[220px] mx-auto">
      <h2 className="text-lg font-semibold mb-2 text-center">Sign in with</h2>
      <GoogleSignInButton className={googleButtonStyle} />

      <LinkedInSignInButton className={linkedinButtonStyle} />
    </div>
  );
}
