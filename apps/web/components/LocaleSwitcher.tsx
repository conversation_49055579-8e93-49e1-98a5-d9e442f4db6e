"use client";

import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "next-intl";

const locales = ["en", "th"] as const;
type Locale = (typeof locales)[number];

export default function LocaleSwitcher() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const switchLocale = (newLocale: Locale) => {
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000; SameSite=Lax`;

    const pathWithoutLocale = pathname.replace(
      new RegExp(`^/(${locales.join("|")})`),
      ""
    );

    router.push(`/${newLocale}${pathWithoutLocale}`);
    router.refresh();
  };

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={() => switchLocale("en")}
        className={`px-3 py-1 rounded transition-colors ${
          locale === "en"
            ? "bg-blue-500 text-white"
            : "bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
        }`}
        disabled={locale === "en"}
        aria-label="Switch to English"
      >
        EN
      </button>
      <button
        onClick={() => switchLocale("th")}
        className={`px-3 py-1 rounded transition-colors ${
          locale === "th"
            ? "bg-blue-500 text-white"
            : "bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
        }`}
        disabled={locale === "th"}
        aria-label="change-to-thai-language"
      >
        ไทย
      </button>
    </div>
  );
}
