"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { GoogleSignInButton, LinkedInSignInButton } from "../components/auth";

export default function RootPage() {
  const router = useRouter();
  const [showLogin, setShowLogin] = useState(false);

  useEffect(() => {
    // Show login options after a brief delay
    const timer = setTimeout(() => {
      setShowLogin(true);
    }, 500);

    // Cleanup timeout
    return () => clearTimeout(timer);
  }, []);

  const handleContinue = () => {
    // Redirect to the default locale
    router.replace("/en");
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-6 bg-white rounded-lg shadow-md">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Welcome to CheckU</h1>
          {!showLogin ? (
            <p className="text-gray-600">Loading options...</p>
          ) : (
            <div className="space-y-6">
              <p className="text-gray-600 mb-4">
                Sign in to continue or proceed as a guest
              </p>

              <div className="space-y-3">
                <GoogleSignInButton className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" />

                <LinkedInSignInButton className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-700 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" />

                <button
                  onClick={handleContinue}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Continue as Guest
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
