import { notFound } from "next/navigation";
import LocaleLayoutClient from "./layout-client";
import { ReactNode } from "react";

export const SUPPORTED_LOCALES = ["en", "th"] as const;
export type Locale = (typeof SUPPORTED_LOCALES)[number];

function isValidLocale(locale: string): locale is Locale {
  return (SUPPORTED_LOCALES as readonly string[]).includes(locale);
}

export async function generateStaticParams() {
  return SUPPORTED_LOCALES.map((locale) => ({
    locale,
  }));
}

export const metadata = {
  title: "My International App",
  description: "Welcome to my international app!",
};

async function LocaleLayoutWithMessages({
  children,
  locale,
}: {
  children: ReactNode;
  locale: Locale;
}) {
  let messages;
  try {
    const messagesModule = await import(`../../messages/${locale}/common.json`);
    messages = messagesModule.default || messagesModule;

    if (!messages) {
      throw new Error(`No messages found for locale: ${locale}`);
    }
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);
    notFound();
  }

  return (
    <LocaleLayoutClient locale={locale} messages={{ common: messages }}>
      {children}
    </LocaleLayoutClient>
  );
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  if (!isValidLocale(locale)) {
    notFound();
  }

  return (
    <LocaleLayoutWithMessages locale={locale}>
      {children}
    </LocaleLayoutWithMessages>
  );
}

export const revalidate = 0;
